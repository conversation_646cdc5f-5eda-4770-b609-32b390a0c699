{"title": "isFrozenUA", "name": "ua-is-frozen", "version": "0.1.2", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "description": "A freeze-test for your user-agent string", "type": "commonjs", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "exports": {".": {"require": "./dist/cjs/index.js", "import": "./dist/esm/index.js"}}, "files": ["dist"], "directories": {"dist": "dist", "src": "src", "test": "test"}, "scripts": {"build:cjs": "tsc --module commonjs --outDir ./dist/cjs --target es5", "build:esm": "tsc --module esnext --outDir ./dist/esm --target es6 && echo '{\"type\":\"module\"}' > ./dist/esm/package.json", "test": "mocha ./test"}, "repository": {"type": "git", "url": "git+https://github.com/faisalman/ua-is-frozen.git"}, "keywords": ["ua-parser-js", "ua-is-frozen", "user-agent", "frozen", "reduced"], "license": "MIT", "bugs": {"url": "https://github.com/faisalman/ua-is-frozen/issues"}, "homepage": "https://github.com/faisalman/ua-is-frozen#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/faisalman"}, {"type": "opencollective", "url": "https://opencollective.com/ua-parser-js"}, {"type": "paypal", "url": "https://paypal.me/faisalman"}], "devDependencies": {"mocha": "^10.2.0", "typescript": "^5.2.2"}}